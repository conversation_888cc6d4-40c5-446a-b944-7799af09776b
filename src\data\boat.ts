import { Varps } from '../api/game/varps'
import { Tile } from '../api/model/tile'

export class BoatRequiredItem {
    itemId: number
    amountToWithdraw: number
    minimumAmount: number
}


export const CharterTiles = {
    Karamja: new Tile(2954, 3152, 0),
    Fortis: new Tile(1743, 3136, 0),
}

export const CharterDestinations  = {
    Aldarin: {
        locationName: 'Aldarin',
        destination: new Tile(1455, 2968, 0),
        requiredItems: [{ itemId: 995, amountToWithdraw: 10000, minimumAmount: 500 }],
        isCharter: true,
    } as Partial<Boat>
}

export class Boat {
    npcId: number
    locationName: string
    tile: Tile
    destination: Tile
    requiredItems?: BoatRequiredItem[]
    name: string
    widgetId?: number
    otherRequirement?: () => boolean
    isCharter?: boolean


    //the fuck is Varps.getVarbit(9650) ???/


    static allBoats: Boat[] = [
        {
            name: 'Brimhaven to Fortis',
            npcId: 1329,
            locationName: 'Civitas illa Fortis',
            tile: new Tile(2759, 3234, 0),
            destination: new Tile(1743, 3136, 0),
            requiredItems: [{ itemId: 995, amountToWithdraw: 10000, minimumAmount: 2400 }],
            widgetId: 4718637,
            otherRequirement: () => Varps.getVarbit(9650) >= 1,
            isCharter: true
        },
        {
            name: 'Brimhaven to Shipyard',
            npcId: 1329,
            tile: new Tile(2759, 3234, 0),
            locationName: 'Shipyard',
            destination: new Tile(3001, 3032, 0),
            requiredItems: [{ itemId: 995, amountToWithdraw: 10000, minimumAmount: 400 }],
            widgetId: 4718603,
            otherRequirement: () => Varps.getVarbit(9650) >= 1,
            isCharter: true
        },
        {
            name: 'Ardougne to Brimhaven',
            npcId: 3650,
            tile: new Tile(2680, 3275, 0),

            locationName: 'Brimhaven',
            destination: new Tile(2772, 3234, 0),
            requiredItems: [{ itemId: 995, amountToWithdraw: 10000, minimumAmount: 500 }],
            isCharter: true
        },
        {
            name: 'Karamja to Aldarin',
            npcId: 1333,
            tile: CharterTiles.Karamja,
            destination: CharterDestinations.Aldarin.destination,
            locationName: CharterDestinations.Aldarin.locationName,
            requiredItems: CharterDestinations.Aldarin.requiredItems,
            isCharter: true,
            ...CharterDestinations.Aldarin,
            otherRequirement: () => Varps.getVarbit(9650) >= 1,
        },
        {
            name: 'Fortis to Aldarin',
            npcId: 1333,
            tile: CharterTiles.Fortis,
            destination: CharterDestinations.Aldarin.destination,
            locationName: CharterDestinations.Aldarin.locationName,
            requiredItems: CharterDestinations.Aldarin.requiredItems,
            isCharter: true,
            ...CharterDestinations.Aldarin,
        }
    ]
}
